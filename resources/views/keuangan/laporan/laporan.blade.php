@extends('layouts.contentNavbarLayout')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card bg-gradient-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h4 class="mb-1">📊 <PERSON><PERSON><PERSON></h4>
              <p class="mb-0 opacity-75">Periode: {{ $tahun ?? date('Y') }} | Update: {{ now()->format('d M Y, H:i') }}</p>
            </div>
            <div class="d-flex gap-2">
              <form method="GET" action="{{ route('laporan') }}" class="d-flex gap-2">
                <select class="form-select form-select-sm" name="tahun" onchange="this.form.submit()" style="width: auto;">
                  @for ($i = now()->year; $i >= 2020; $i--)
                  <option value="{{ $i }}" {{ $i == ($tahun ?? date('Y')) ? 'selected' : '' }}>{{ $i }}</option>
                  @endfor
                </select>
                <select class="form-select form-select-sm" name="bulan" onchange="this.form.submit()" style="width: auto;">
                  <option value="">Semua Bulan</option>
                  @for ($i = 1; $i <= 12; $i++)
                  <option value="{{ $i }}" {{ $i == request('bulan') ? 'selected' : '' }}>
                    {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                  </option>
                  @endfor
                </select>
              </form>
              <button class="btn btn-light btn-sm" onclick="exportToExcel()">
                <i class="bx bx-download"></i> Export
              </button>
              <button class="btn btn-light btn-sm" onclick="window.print()">
                <i class="bx bx-printer"></i> Print
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-xl-2 col-md-4 col-6 mb-3">
      <div class="card border-start border-success border-4">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <div class="avatar-initial bg-success rounded">
              <i class="bx bx-trending-up"></i>
            </div>
          </div>
          <h6 class="card-title">Pendapatan Langganan</h6>
          <h5 class="text-success">Rp {{ number_format($totalPendapatanLangganan ?? 0, 0, ',', '.') }}</h5>
        </div>
      </div>
    </div>

    <div class="col-xl-2 col-md-4 col-6 mb-3">
      <div class="card border-start border-info border-4">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <div class="avatar-initial bg-info rounded">
              <i class="bx bx-wallet"></i>
            </div>
          </div>
          <h6 class="card-title">Pendapatan Non-Langganan</h6>
          <h5 class="text-info">Rp {{ number_format($totalPendapatanLain ?? 0, 0, ',', '.') }}</h5>
        </div>
      </div>
    </div>

    <div class="col-xl-2 col-md-4 col-6 mb-3">
      <div class="card border-start border-warning border-4">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <div class="avatar-initial bg-warning rounded">
              <i class="bx bx-money"></i>
            </div>
          </div>
          <h6 class="card-title">Total Pendapatan</h6>
          <h5 class="text-warning">Rp {{ number_format(($totalPendapatanLangganan ?? 0) + ($totalPendapatanLain ?? 0), 0, ',', '.') }}</h5>
        </div>
      </div>
    </div>

    <div class="col-xl-2 col-md-4 col-6 mb-3">
      <div class="card border-start border-danger border-4">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <div class="avatar-initial bg-danger rounded">
              <i class="bx bx-trending-down"></i>
            </div>
          </div>
          <h6 class="card-title">Total Pengeluaran</h6>
          <h5 class="text-danger">Rp {{ number_format($totalPengeluaran ?? 0, 0, ',', '.') }}</h5>
        </div>
      </div>
    </div>

    <div class="col-xl-2 col-md-4 col-6 mb-3">
      <div class="card border-start border-{{ ($totalLabaRugi ?? 0) >= 0 ? 'success' : 'danger' }} border-4">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <div class="avatar-initial bg-{{ ($totalLabaRugi ?? 0) >= 0 ? 'success' : 'danger' }} rounded">
              <i class="bx bx-{{ ($totalLabaRugi ?? 0) >= 0 ? 'trending-up' : 'trending-down' }}"></i>
            </div>
          </div>
          <h6 class="card-title">{{ ($totalLabaRugi ?? 0) >= 0 ? 'Laba' : 'Rugi' }}</h6>
          <h5 class="text-{{ ($totalLabaRugi ?? 0) >= 0 ? 'success' : 'danger' }}">Rp {{ number_format($totalLabaRugi ?? 0, 0, ',', '.') }}</h5>
        </div>
      </div>
    </div>

    <div class="col-xl-2 col-md-4 col-6 mb-3">
      <div class="card border-start border-primary border-4">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <div class="avatar-initial bg-primary rounded">
              <i class="bx bx-wallet-alt"></i>
            </div>
          </div>
          <h6 class="card-title">Total Kas</h6>
          <h5 class="text-primary">Rp {{ number_format(($kasBesar ?? 0) + ($kasKecil ?? 0), 0, ',', '.') }}</h5>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="row mb-4">
    <!-- Main Financial Chart -->
    <div class="col-xl-8 col-12 mb-4">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="bx bx-line-chart me-2"></i>Tren Keuangan Bulanan
          </h5>
          <div class="d-flex gap-2">
            <select class="form-select form-select-sm" id="chartType" onchange="changeChartType()">
              <option value="line">Line Chart</option>
              <option value="bar">Bar Chart</option>
              <option value="area">Area Chart</option>
            </select>
            <button class="btn btn-sm btn-outline-primary" onclick="toggleFullscreen()">
              <i class="bx bx-fullscreen"></i>
            </button>
          </div>
        </div>
        <div class="card-body">
          <canvas id="mainChart" height="400"></canvas>
        </div>
      </div>
    </div>

    <!-- Revenue Distribution Pie Chart -->
    <div class="col-xl-4 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="bx bx-pie-chart-alt me-2"></i>Distribusi Pendapatan
          </h6>
        </div>
        <div class="card-body">
          <canvas id="pieChart" height="300"></canvas>
          <div class="mt-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span class="d-flex align-items-center">
                <span class="badge bg-success me-2" style="width: 12px; height: 12px;"></span>
                Langganan
              </span>
              <span class="fw-bold">{{ $totalPendapatanLangganan > 0 ? number_format(($totalPendapatanLangganan / (($totalPendapatanLangganan ?? 0) + ($totalPendapatanLain ?? 0))) * 100, 1) : 0 }}%</span>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <span class="d-flex align-items-center">
                <span class="badge bg-info me-2" style="width: 12px; height: 12px;"></span>
                Non-Langganan
              </span>
              <span class="fw-bold">{{ $totalPendapatanLain > 0 ? number_format(($totalPendapatanLain / (($totalPendapatanLangganan ?? 0) + ($totalPendapatanLain ?? 0))) * 100, 1) : 0 }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Secondary Charts -->
  <div class="row mb-4">
    <!-- Monthly Comparison Chart -->
    <div class="col-xl-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="bx bx-bar-chart-alt me-2"></i>Perbandingan Bulanan
          </h6>
        </div>
        <div class="card-body">
          <canvas id="comparisonChart" height="300"></canvas>
        </div>
      </div>
    </div>

    <!-- Cash Flow Chart -->
    <div class="col-xl-6 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="bx bx-trending-up me-2"></i>Arus Kas
          </h6>
        </div>
        <div class="card-body">
          <canvas id="cashFlowChart" height="300"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Financial Table -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="bx bx-table me-2"></i>Detail Laporan Keuangan Bulanan
          </h5>
          <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-success" onclick="exportTableToExcel()">
              <i class="bx bx-download me-1"></i>Export Excel
            </button>
            <button class="btn btn-sm btn-outline-info" onclick="exportTableToPDF()">
              <i class="bx bx-file-pdf me-1"></i>Export PDF
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover table-striped" id="financialTable">
              <thead class="table-dark">
                <tr>
                  <th rowspan="2" class="text-center align-middle">Bulan</th>
                  <th colspan="3" class="text-center">Pendapatan</th>
                  <th rowspan="2" class="text-center align-middle">Pengeluaran</th>
                  <th rowspan="2" class="text-center align-middle">Laba/Rugi</th>
                  <th rowspan="2" class="text-center align-middle">Selisih (%)</th>
                  <th rowspan="2" class="text-center align-middle">Status</th>
                </tr>
                <tr>
                  <th class="text-center">Langganan</th>
                  <th class="text-center">Non-Langganan</th>
                  <th class="text-center">Total</th>
                </tr>
              </thead>
              <tbody>
                @if(isset($laporan) && count($laporan) > 0)
                  @foreach($laporan as $index => $item)
                  @php
                    $selisih = $item['pendapatan'] > 0 ? (($item['pendapatan'] - $item['pengeluaran']) / $item['pendapatan']) * 100 : 0;
                    $statusClass = $item['laba_rugi'] >= 0 ? 'success' : 'danger';
                    $statusIcon = $item['laba_rugi'] >= 0 ? 'trending-up' : 'trending-down';
                  @endphp
                  <tr>
                    <td class="fw-bold">{{ $item['bulan'] }}</td>
                    <td class="text-end text-success">
                      Rp {{ number_format($item['pendapatan_langganan'], 0, ',', '.') }}
                    </td>
                    <td class="text-end text-info">
                      Rp {{ number_format($item['pendapatan_nonlangganan'], 0, ',', '.') }}
                    </td>
                    <td class="text-end text-primary fw-bold">
                      Rp {{ number_format($item['pendapatan'], 0, ',', '.') }}
                    </td>
                    <td class="text-end text-danger">
                      Rp {{ number_format($item['pengeluaran'], 0, ',', '.') }}
                    </td>
                    <td class="text-end text-{{ $statusClass }} fw-bold">
                      Rp {{ number_format($item['laba_rugi'], 0, ',', '.') }}
                    </td>
                    <td class="text-center">
                      <span class="badge bg-{{ $selisih >= 20 ? 'success' : ($selisih >= 0 ? 'warning' : 'danger') }}">
                        {{ number_format($selisih, 1) }}%
                      </span>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-{{ $statusClass }}">
                        <i class="bx bx-{{ $statusIcon }} me-1"></i>{{ $item['status'] }}
                      </span>
                    </td>
                  </tr>
                  @endforeach
                @else
                  @for($i = 1; $i <= 12; $i++)
                  <tr>
                    <td class="fw-bold">{{ DateTime::createFromFormat('!m', $i)->format('F') }}</td>
                    <td class="text-end text-muted">Rp 0</td>
                    <td class="text-end text-muted">Rp 0</td>
                    <td class="text-end text-muted">Rp 0</td>
                    <td class="text-end text-muted">Rp 0</td>
                    <td class="text-end text-muted">Rp 0</td>
                    <td class="text-center">
                      <span class="badge bg-secondary">0%</span>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-secondary">-</span>
                    </td>
                  </tr>
                  @endfor
                @endif
              </tbody>
              <tfoot class="table-dark">
                <tr>
                  <th class="text-center">TOTAL</th>
                  <th class="text-end">Rp {{ number_format($totalPendapatanLangganan ?? 0, 0, ',', '.') }}</th>
                  <th class="text-end">Rp {{ number_format($totalPendapatanLain ?? 0, 0, ',', '.') }}</th>
                  <th class="text-end">Rp {{ number_format(($totalPendapatanLangganan ?? 0) + ($totalPendapatanLain ?? 0), 0, ',', '.') }}</th>
                  <th class="text-end">Rp {{ number_format($totalPengeluaran ?? 0, 0, ',', '.') }}</th>
                  <th class="text-end">Rp {{ number_format($totalLabaRugi ?? 0, 0, ',', '.') }}</th>
                  <th class="text-center">
                    @php
                      $totalPendapatan = ($totalPendapatanLangganan ?? 0) + ($totalPendapatanLain ?? 0);
                      $totalSelisih = $totalPendapatan > 0 ? (($totalPendapatan - ($totalPengeluaran ?? 0)) / $totalPendapatan) * 100 : 0;
                    @endphp
                    <span class="badge bg-{{ $totalSelisih >= 20 ? 'success' : ($totalSelisih >= 0 ? 'warning' : 'danger') }}">
                      {{ number_format($totalSelisih, 1) }}%
                    </span>
                  </th>
                  <th class="text-center">
                    <span class="badge bg-{{ ($totalLabaRugi ?? 0) >= 0 ? 'success' : 'danger' }}">
                      {{ ($totalLabaRugi ?? 0) >= 0 ? 'LABA' : 'RUGI' }}
                    </span>
                  </th>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- RAB and Cash Management -->
  <div class="row mb-4">
    <!-- RAB (Budget) Table -->
    <div class="col-xl-8 col-12 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="bx bx-calculator me-2"></i>Rencana Anggaran Biaya (RAB)
          </h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Keterangan</th>
                  <th class="text-end">Anggaran</th>
                  <th class="text-end">Realisasi</th>
                  <th class="text-end">Sisa</th>
                  <th class="text-center">Progress</th>
                </tr>
              </thead>
              <tbody>
                @if(isset($rabData) && count($rabData) > 0)
                  @foreach($rabData as $rab)
                  @php
                    $progress = $rab['anggaran'] > 0 ? ($rab['realisasi'] / $rab['anggaran']) * 100 : 0;
                    $progressClass = $progress <= 50 ? 'success' : ($progress <= 80 ? 'warning' : 'danger');
                  @endphp
                  <tr>
                    <td class="fw-bold">{{ $rab['nama'] }}</td>
                    <td class="text-end">Rp {{ number_format($rab['anggaran'], 0, ',', '.') }}</td>
                    <td class="text-end">Rp {{ number_format($rab['realisasi'], 0, ',', '.') }}</td>
                    <td class="text-end">Rp {{ number_format($rab['sisa'], 0, ',', '.') }}</td>
                    <td class="text-center">
                      <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-{{ $progressClass }}" style="width: {{ min(100, $progress) }}%"></div>
                      </div>
                      <small class="text-muted">{{ number_format($progress, 1) }}%</small>
                    </td>
                  </tr>
                  @endforeach
                @else
                  <tr>
                    <td colspan="5" class="text-center text-muted">Tidak ada data RAB untuk periode ini</td>
                  </tr>
                @endif
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Cash Management -->
    <div class="col-xl-4 col-12 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="bx bx-wallet me-2"></i>Manajemen Kas
          </h5>
        </div>
        <div class="card-body">
          <!-- Kas Besar -->
          <div class="card bg-light mb-3">
            <div class="card-body text-center">
              <div class="avatar mx-auto mb-2">
                <div class="avatar-initial bg-primary rounded">
                  <i class="bx bx-building-house"></i>
                </div>
              </div>
              <h6 class="card-title">Kas Besar</h6>
              <h4 class="text-primary mb-0">Rp {{ number_format($kasBesar ?? 0, 0, ',', '.') }}</h4>
            </div>
          </div>

          <!-- Kas Kecil -->
          <div class="card bg-light mb-3">
            <div class="card-body text-center">
              <div class="avatar mx-auto mb-2">
                <div class="avatar-initial bg-info rounded">
                  <i class="bx bx-wallet-alt"></i>
                </div>
              </div>
              <h6 class="card-title">Kas Kecil</h6>
              <h4 class="text-info mb-0">Rp {{ number_format($kasKecil ?? 0, 0, ',', '.') }}</h4>
            </div>
          </div>

          <!-- Total Kas -->
          <div class="card bg-gradient-success text-white">
            <div class="card-body text-center">
              <div class="avatar mx-auto mb-2">
                <div class="avatar-initial bg-white text-success rounded">
                  <i class="bx bx-money"></i>
                </div>
              </div>
              <h6 class="card-title text-white">Total Kas</h6>
              <h4 class="text-white mb-0">Rp {{ number_format(($kasBesar ?? 0) + ($kasKecil ?? 0), 0, ',', '.') }}</h4>
            </div>
          </div>

          <!-- Cash Flow Indicator -->
          <div class="mt-3">
            @php
              $totalPendapatan = ($totalPendapatanLangganan ?? 0) + ($totalPendapatanLain ?? 0);
              $cashRatio = $totalPendapatan > 0 ? ((($kasBesar ?? 0) + ($kasKecil ?? 0)) / $totalPendapatan) * 100 : 0;
            @endphp
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span>Cash Ratio:</span>
              <span class="badge bg-{{ $cashRatio >= 20 ? 'success' : ($cashRatio >= 10 ? 'warning' : 'danger') }}">
                {{ number_format($cashRatio, 1) }}%
              </span>
            </div>
            <div class="progress" style="height: 8px;">
              <div class="progress-bar bg-{{ $cashRatio >= 20 ? 'success' : ($cashRatio >= 10 ? 'warning' : 'danger') }}"
                   style="width: {{ min(100, $cashRatio) }}%"></div>
            </div>
            <small class="text-muted">Rasio kas terhadap pendapatan</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js and Export Libraries -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
// Chart data from Laravel
const chartData = {
    labels: @json(isset($laporan) ? array_column($laporan, 'bulan') : ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des']),
    pendapatanLangganan: @json(isset($laporan) ? array_column($laporan, 'pendapatan_langganan') : array_fill(0, 12, 0)),
    pendapatanNonLangganan: @json(isset($laporan) ? array_column($laporan, 'pendapatan_nonlangganan') : array_fill(0, 12, 0)),
    totalPendapatan: @json(isset($laporan) ? array_column($laporan, 'pendapatan') : array_fill(0, 12, 0)),
    pengeluaran: @json(isset($laporan) ? array_column($laporan, 'pengeluaran') : array_fill(0, 12, 0)),
    labaRugi: @json(isset($laporan) ? array_column($laporan, 'laba_rugi') : array_fill(0, 12, 0))
};

// Chart configuration
const chartConfig = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
                label: function(context) {
                    return context.dataset.label + ': Rp ' +
                           new Intl.NumberFormat('id-ID').format(context.parsed.y);
                }
            }
        }
    },
    scales: {
        x: {
            display: true,
            title: {
                display: true,
                text: 'Bulan'
            }
        },
        y: {
            display: true,
            title: {
                display: true,
                text: 'Jumlah (Rp)'
            },
            ticks: {
                callback: function(value) {
                    return 'Rp ' + new Intl.NumberFormat('id-ID', {
                        notation: 'compact',
                        compactDisplay: 'short'
                    }).format(value);
                }
            }
        }
    }
};

// Chart instances
let mainChart, pieChart, comparisonChart, cashFlowChart;

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeMainChart();
    initializePieChart();
    initializeComparisonChart();
    initializeCashFlowChart();
});

// Main Financial Chart
function initializeMainChart() {
    const ctx = document.getElementById('mainChart');
    if (!ctx) return;

    mainChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [
                {
                    label: 'Total Pendapatan',
                    data: chartData.totalPendapatan,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Total Pengeluaran',
                    data: chartData.pengeluaran,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Laba/Rugi',
                    data: chartData.labaRugi,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: chartConfig
    });
}

// Revenue Distribution Pie Chart
function initializePieChart() {
    const ctx = document.getElementById('pieChart');
    if (!ctx) return;

    const totalLangganan = {{ $totalPendapatanLangganan ?? 0 }};
    const totalNonLangganan = {{ $totalPendapatanLain ?? 0 }};

    pieChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['Pendapatan Langganan', 'Pendapatan Non-Langganan'],
            datasets: [{
                data: [totalLangganan, totalNonLangganan],
                backgroundColor: [
                    '#28a745',
                    '#17a2b8'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : '0.0';
                            return context.label + ': Rp ' +
                                   new Intl.NumberFormat('id-ID').format(context.parsed) +
                                   ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
}

// Monthly Comparison Chart
function initializeComparisonChart() {
    const ctx = document.getElementById('comparisonChart');
    if (!ctx) return;

    comparisonChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.labels,
            datasets: [
                {
                    label: 'Pendapatan Langganan',
                    data: chartData.pendapatanLangganan,
                    backgroundColor: 'rgba(40, 167, 69, 0.8)',
                    borderColor: '#28a745',
                    borderWidth: 1
                },
                {
                    label: 'Pendapatan Non-Langganan',
                    data: chartData.pendapatanNonLangganan,
                    backgroundColor: 'rgba(23, 162, 184, 0.8)',
                    borderColor: '#17a2b8',
                    borderWidth: 1
                },
                {
                    label: 'Pengeluaran',
                    data: chartData.pengeluaran,
                    backgroundColor: 'rgba(220, 53, 69, 0.8)',
                    borderColor: '#dc3545',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': Rp ' +
                                   new Intl.NumberFormat('id-ID').format(context.parsed.y);
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Bulan'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Jumlah (Rp)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID', {
                                notation: 'compact',
                                compactDisplay: 'short'
                            }).format(value);
                        }
                    }
                }
            }
        }
    });
}

// Cash Flow Chart
function initializeCashFlowChart() {
    const ctx = document.getElementById('cashFlowChart');
    if (!ctx) return;

    // Calculate cumulative cash flow
    let cumulativeCashFlow = [];
    let runningTotal = 0;

    chartData.labaRugi.forEach(value => {
        runningTotal += value;
        cumulativeCashFlow.push(runningTotal);
    });

    cashFlowChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [
                {
                    label: 'Arus Kas Bulanan',
                    data: chartData.labaRugi,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Arus Kas Kumulatif',
                    data: cumulativeCashFlow,
                    borderColor: '#6f42c1',
                    backgroundColor: 'rgba(111, 66, 193, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': Rp ' +
                                   new Intl.NumberFormat('id-ID').format(context.parsed.y);
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Bulan'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Arus Kas (Rp)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID', {
                                notation: 'compact',
                                compactDisplay: 'short'
                            }).format(value);
                        }
                    }
                }
            }
        }
    });
}

// Chart Type Changer
function changeChartType() {
    const selectedType = document.getElementById('chartType').value;

    if (mainChart) {
        mainChart.destroy();
    }

    const ctx = document.getElementById('mainChart');

    const datasets = [
        {
            label: 'Total Pendapatan',
            data: chartData.totalPendapatan,
            borderColor: '#28a745',
            backgroundColor: selectedType === 'area' ? 'rgba(40, 167, 69, 0.3)' : 'rgba(40, 167, 69, 0.1)',
            borderWidth: 3,
            fill: selectedType === 'area',
            tension: selectedType === 'line' || selectedType === 'area' ? 0.4 : 0
        },
        {
            label: 'Total Pengeluaran',
            data: chartData.pengeluaran,
            borderColor: '#dc3545',
            backgroundColor: selectedType === 'area' ? 'rgba(220, 53, 69, 0.3)' : 'rgba(220, 53, 69, 0.1)',
            borderWidth: 3,
            fill: selectedType === 'area',
            tension: selectedType === 'line' || selectedType === 'area' ? 0.4 : 0
        }
    ];

    mainChart = new Chart(ctx, {
        type: selectedType === 'area' ? 'line' : selectedType,
        data: {
            labels: chartData.labels,
            datasets: datasets
        },
        options: chartConfig
    });
}

// Export Functions
function exportToExcel() {
    const table = document.getElementById('financialTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: 'Laporan Keuangan'});
    XLSX.writeFile(wb, `laporan_keuangan_{{ $tahun ?? date('Y') }}.xlsx`);
}

function exportTableToExcel() {
    exportToExcel();
}

function exportTableToPDF() {
    window.print();
}

// Toggle Fullscreen
function toggleFullscreen() {
    const chartContainer = document.querySelector('#mainChart').closest('.card');

    if (!document.fullscreenElement) {
        chartContainer.requestFullscreen().then(() => {
            chartContainer.style.position = 'fixed';
            chartContainer.style.top = '0';
            chartContainer.style.left = '0';
            chartContainer.style.width = '100vw';
            chartContainer.style.height = '100vh';
            chartContainer.style.zIndex = '9999';
            chartContainer.style.backgroundColor = 'white';

            setTimeout(() => {
                if (mainChart) mainChart.resize();
            }, 100);
        });
    } else {
        document.exitFullscreen().then(() => {
            chartContainer.style.position = '';
            chartContainer.style.top = '';
            chartContainer.style.left = '';
            chartContainer.style.width = '';
            chartContainer.style.height = '';
            chartContainer.style.zIndex = '';
            chartContainer.style.backgroundColor = '';

            setTimeout(() => {
                if (mainChart) mainChart.resize();
            }, 100);
        });
    }
}

// Responsive chart resize
window.addEventListener('resize', function() {
    setTimeout(() => {
        if (mainChart) mainChart.resize();
        if (pieChart) pieChart.resize();
        if (comparisonChart) comparisonChart.resize();
        if (cashFlowChart) cashFlowChart.resize();
    }, 100);
});

// Print optimization
window.addEventListener('beforeprint', function() {
    if (mainChart) mainChart.resize();
    if (pieChart) pieChart.resize();
    if (comparisonChart) comparisonChart.resize();
    if (cashFlowChart) cashFlowChart.resize();
});

// Auto-refresh data every 5 minutes
setInterval(function() {
    // You can implement auto-refresh here if needed
    console.log('Auto-refresh check...');
}, 300000);
</script>

@endsection
